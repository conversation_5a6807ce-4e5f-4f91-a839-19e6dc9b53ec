import { Injectable, Logger } from '@nestjs/common';
import {
  ILlmProvider,
  ILlmProviderFactory,
  LlmProviderConfig,
  LlmProviderType
} from '../interfaces/llm-provider.interface';
import { GoogleProvider } from './google.provider';
import { VllmProvider } from './vllm.provider';

@Injectable()
export class LlmProviderFactory implements ILlmProviderFactory {
  private readonly logger = new Logger(LlmProviderFactory.name);

  createProvider(type: LlmProviderType, config: LlmProviderConfig): ILlmProvider {
    this.logger.debug(`Creating provider of type: ${type}`);

    switch (type) {
      case LlmProviderType.GOOGLE:
        return new GoogleProvider(config);

      case LlmProviderType.VLLM:
        return new VllmProvider(config);

      case LlmProviderType.ANTHROPIC:
        // Placeholder for future Anthropic implementation
        throw new Error('Anthropic provider not yet implemented');

      default:
        throw new Error(`Unsupported provider type: ${type}`);
    }
  }

  getSupportedProviders(): LlmProviderType[] {
    return [
      LlmProviderType.GOOGLE,
      LlmProviderType.VLLM,
      // LlmProviderType.ANTHROPIC, // Uncomment when implemented
    ];
  }
}
