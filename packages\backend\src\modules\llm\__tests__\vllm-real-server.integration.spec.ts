import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { LlmService } from '../llm.service';
import { LlmProviderFactory } from '../providers/llm-provider.factory';
import { ChatMessage, Tool } from '@otrs-ai-powered/shared';
import { VllmProvider } from '../providers/vllm.provider';
import { getTestConfig, shouldSkipExternalServices, createTestDescription } from '../../../test/test-config.util';

describe('vLLM Real Server Integration Tests', () => {
  let llmService: LlmService;
  let vllmProvider: VllmProvider;
  let testConfig: ReturnType<typeof getTestConfig>;

  beforeAll(() => {
    if (shouldSkipExternalServices()) {
      console.log('Skipping vLLM real server tests - external services disabled');
      return;
    }

    try {
      testConfig = getTestConfig();
    } catch (error) {
      console.log('Skipping vLLM real server tests - configuration missing:', error);
      return;
    }
  });

  beforeAll(async () => {
    if (shouldSkipExternalServices() || !testConfig) {
      return;
    }

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LlmService,
        LlmProviderFactory,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: any) => {
              const config: Record<string, any> = {
                'llm.provider': 'vllm',
                'llm.apiKey': testConfig.llm.apiKey,
                'llm.model': testConfig.llm.model,
                'llm.temperature': testConfig.llm.temperature,
                'llm.maxTokens': testConfig.llm.maxTokens,
                'llm.timeout': testConfig.llm.timeout,
                'llm.baseUrl': testConfig.llm.baseUrl,
              };
              return config[key] ?? defaultValue;
            }),
          },
        },
      ],
    }).compile();

    llmService = module.get<LlmService>(LlmService);

    // Initialize the service
    await llmService.onModuleInit();

    // Create a direct provider instance for low-level testing
    vllmProvider = new VllmProvider(testConfig.llm);
  }, 30000); // 30 second timeout for setup

  describe('Server Connectivity and Authentication', () => {
    it('should successfully connect to the real vLLM server', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled or configuration missing');
        return;
      }

      const isAvailable = await llmService.isAvailable();
      expect(isAvailable).toBe(true);
    }, 30000);

    it('should validate provider configuration', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled or configuration missing');
        return;
      }

      const isValid = await vllmProvider.validateConfig();
      expect(isValid).toBe(true);
    }, 30000);

    it('should return correct provider information', () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled or configuration missing');
        return;
      }

      const providerInfo = llmService.getProviderInfo();
      expect(providerInfo).not.toBeNull();
      expect(providerInfo!.name).toBe('vllm');
      expect(providerInfo!.model).toBe(testConfig.llm.model);
    });
  });

  describe('Basic AI Assistant Functionality', () => {
    it('should handle simple question-answer conversation', async () => {
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'system',
          content: 'You are a helpful AI assistant for OTRS support system.',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          role: 'user',
          content: 'Hello! What is OTRS?',
          timestamp: new Date().toISOString(),
        },
      ];

      const response = await llmService.generateResponse(messages);

      expect(response.content).toBeDefined();
      expect(response.content.length).toBeGreaterThan(10);
      expect(response.content.toLowerCase()).toContain('otrs');
      console.log('Simple Q&A Response:', response.content);
    }, 45000);

    it('should handle multi-turn dialogue', async () => {
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'system',
          content: 'You are a helpful AI assistant. Keep responses concise.',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          role: 'user',
          content: 'What is the weather like?',
          timestamp: new Date().toISOString(),
        },
        {
          id: '3',
          role: 'assistant',
          content: 'I don\'t have access to current weather data. Could you tell me your location so I can help you find weather information?',
          timestamp: new Date().toISOString(),
        },
        {
          id: '4',
          role: 'user',
          content: 'I am in Paris, France.',
          timestamp: new Date().toISOString(),
        },
      ];

      const response = await llmService.generateResponse(messages);

      expect(response.content).toBeDefined();
      expect(response.content.length).toBeGreaterThan(10);
      expect(response.content.toLowerCase()).toContain('paris');
      console.log('Multi-turn Response:', response.content);
    }, 45000);

    it('should process system messages correctly with Phi-4 format', async () => {
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'system',
          content: 'You are a technical support specialist. Always mention "OTRS" in your responses.',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          role: 'user',
          content: 'I need help with my account.',
          timestamp: new Date().toISOString(),
        },
      ];

      const response = await llmService.generateResponse(messages);

      expect(response.content).toBeDefined();
      expect(response.content.toLowerCase()).toContain('otrs');
      console.log('System Message Response:', response.content);
    }, 45000);
  });

  describe('Tool Execution (MCP Integration)', () => {
    const createTicketTool: Tool = {
      type: 'function',
      name: 'create_ticket',
      description: 'Create a new OTRS support ticket',
      parameters: {
        type: 'object',
        properties: {
          title: {
            type: 'string',
            description: 'The title of the ticket',
          },
          description: {
            type: 'string',
            description: 'Detailed description of the issue',
          },
          priority: {
            type: 'string',
            enum: ['low', 'medium', 'high'],
            description: 'Priority level of the ticket',
          },
          customer_email: {
            type: 'string',
            description: 'Email address of the customer',
          },
        },
        required: ['title', 'description'],
      },
    };

    it('should handle function calling requests', async () => {
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'system',
          content: 'You are an OTRS support assistant. When users report issues, create tickets for them using the create_ticket function.',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          role: 'user',
          content: 'I have a problem with my email not working. My <NAME_EMAIL> and this is urgent.',
          timestamp: new Date().toISOString(),
        },
      ];

      const response = await llmService.generateResponse(messages, [createTicketTool]);

      expect(response.content).toBeDefined();
      console.log('Tool Calling Response:', response.content);
      
      if (response.toolCalls && response.toolCalls.length > 0) {
        console.log('Tool Calls:', JSON.stringify(response.toolCalls, null, 2));
        
        const toolCall = response.toolCalls[0];
        expect(toolCall.toolName).toBe('create_ticket');
        expect(toolCall.arguments).toHaveProperty('title');
        expect(toolCall.arguments).toHaveProperty('description');
        
        // Validate the arguments make sense
        expect(toolCall.arguments.title).toContain('email');
        expect(toolCall.arguments.description).toContain('email');
      } else {
        console.log('No tool calls detected in response');
      }
    }, 60000);

    it('should handle tool argument parsing and validation', async () => {
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'user',
          content: 'Create a high priority ticket for database connection issues <NAME_EMAIL>',
          timestamp: new Date().toISOString(),
        },
      ];

      const response = await llmService.generateResponse(messages, [createTicketTool]);

      if (response.toolCalls && response.toolCalls.length > 0) {
        const toolCall = response.toolCalls[0];
        
        expect(toolCall.arguments).toHaveProperty('title');
        expect(toolCall.arguments).toHaveProperty('description');
        
        if (toolCall.arguments.priority) {
          expect(['low', 'medium', 'high']).toContain(toolCall.arguments.priority);
        }
        
        if (toolCall.arguments.customer_email) {
          expect(toolCall.arguments.customer_email).toContain('@');
        }
        
        console.log('Validated Tool Arguments:', toolCall.arguments);
      }
    }, 60000);
  });

  describe('Performance and Response Quality', () => {
    it('should respond within reasonable time limits', async () => {
      const startTime = Date.now();
      
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'user',
          content: 'What are the main features of OTRS?',
          timestamp: new Date().toISOString(),
        },
      ];

      const response = await llmService.generateResponse(messages);
      const responseTime = Date.now() - startTime;

      expect(response.content).toBeDefined();
      expect(responseTime).toBeLessThan(30000); // Should respond within 30 seconds
      
      console.log(`Response time: ${responseTime}ms`);
      console.log(`Response length: ${response.content.length} characters`);
    }, 45000);

    it('should handle concurrent requests', async () => {
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'user',
          content: 'Tell me about OTRS in one sentence.',
          timestamp: new Date().toISOString(),
        },
      ];

      const promises = Array(3).fill(null).map((_, index) => 
        llmService.generateResponse([
          ...messages,
          {
            id: `${index + 2}`,
            role: 'user',
            content: `Request number ${index + 1}`,
            timestamp: new Date().toISOString(),
          },
        ])
      );

      const responses = await Promise.all(promises);

      responses.forEach((response, index) => {
        expect(response.content).toBeDefined();
        expect(response.content.length).toBeGreaterThan(5);
        console.log(`Concurrent Response ${index + 1}:`, response.content.substring(0, 100) + '...');
      });
    }, 90000);
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle empty user messages gracefully', async () => {
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'system',
          content: 'You are a helpful assistant.',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          role: 'user',
          content: '',
          timestamp: new Date().toISOString(),
        },
      ];

      const response = await llmService.generateResponse(messages);

      expect(response.content).toBeDefined();
      expect(response.content.length).toBeGreaterThan(0);
      console.log('Empty message response:', response.content);
    }, 45000);

    it('should handle very long messages', async () => {
      const longMessage = 'This is a very long message. '.repeat(100);
      
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'user',
          content: longMessage + ' Please summarize this message.',
          timestamp: new Date().toISOString(),
        },
      ];

      const response = await llmService.generateResponse(messages);

      expect(response.content).toBeDefined();
      expect(response.content.length).toBeGreaterThan(10);
      console.log('Long message response length:', response.content.length);
    }, 60000);
  });

  describe('Chat Format Validation', () => {
    it('should properly format Phi-4 chat messages for the real server', async () => {
      // This test validates that our chat format is working with the real Phi-4 model
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'system',
          content: 'You are an AI assistant. Respond with exactly "FORMAT_TEST_SUCCESS" if you understand this message.',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          role: 'user',
          content: 'Please confirm you received the system message correctly.',
          timestamp: new Date().toISOString(),
        },
      ];

      const response = await llmService.generateResponse(messages);

      expect(response.content).toBeDefined();
      // The model should understand the system message and respond appropriately
      expect(response.content.toLowerCase()).toMatch(/format|test|success|confirm|understand|received/);
      console.log('Format validation response:', response.content);
    }, 45000);

    it('should handle special characters and formatting in messages', async () => {
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'user',
          content: 'Can you help with this code: `console.log("Hello <|system|> World");` and explain what it does?',
          timestamp: new Date().toISOString(),
        },
      ];

      const response = await llmService.generateResponse(messages);

      expect(response.content).toBeDefined();
      expect(response.content.toLowerCase()).toContain('console');
      console.log('Special characters response:', response.content);
    }, 45000);
  });

  describe('RAG Functionality Simulation', () => {
    it('should handle FAQ-style questions that would benefit from RAG', async () => {
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'system',
          content: 'You are an OTRS support assistant. Here is some FAQ information: OTRS is a ticketing system used for customer support. Common issues include email configuration, user permissions, and ticket routing.',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          role: 'user',
          content: 'How do I configure email settings in OTRS?',
          timestamp: new Date().toISOString(),
        },
      ];

      const response = await llmService.generateResponse(messages);

      expect(response.content).toBeDefined();
      expect(response.content.toLowerCase()).toMatch(/email|configuration|setting|otrs/);
      console.log('RAG-style FAQ response:', response.content);
    }, 45000);

    it('should provide context-aware responses', async () => {
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'system',
          content: 'You are helping with OTRS troubleshooting. The user is experiencing ticket routing issues.',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          role: 'user',
          content: 'My tickets are not being assigned to the right queue.',
          timestamp: new Date().toISOString(),
        },
      ];

      const response = await llmService.generateResponse(messages);

      expect(response.content).toBeDefined();
      expect(response.content.toLowerCase()).toMatch(/queue|routing|ticket|assign/);
      console.log('Context-aware response:', response.content);
    }, 45000);
  });

  describe('Advanced Integration Scenarios', () => {
    it('should handle mixed tool calling and conversation', async () => {
      const searchTool: Tool = {
        type: 'function',
        name: 'search_knowledge_base',
        description: 'Search the OTRS knowledge base for relevant articles',
        parameters: {
          type: 'object',
          properties: {
            query: {
              type: 'string',
              description: 'Search query for the knowledge base',
            },
            category: {
              type: 'string',
              description: 'Category to search within',
            },
          },
          required: ['query'],
        },
      };

      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'system',
          content: 'You are an OTRS support assistant. Use the search_knowledge_base function to find relevant information when users ask questions.',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          role: 'user',
          content: 'I need help with OTRS email configuration. Can you find some documentation for me?',
          timestamp: new Date().toISOString(),
        },
      ];

      const response = await llmService.generateResponse(messages, [searchTool]);

      expect(response.content).toBeDefined();
      console.log('Mixed tool/conversation response:', response.content);

      if (response.toolCalls) {
        console.log('Knowledge base search calls:', JSON.stringify(response.toolCalls, null, 2));
      }
    }, 60000);

    it('should maintain conversation context across multiple exchanges', async () => {
      // Simulate a multi-turn conversation
      let messages: ChatMessage[] = [
        {
          id: '1',
          role: 'system',
          content: 'You are an OTRS support specialist. Remember the context of the conversation.',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          role: 'user',
          content: 'I am having trouble with OTRS ticket notifications.',
          timestamp: new Date().toISOString(),
        },
      ];

      // First exchange
      let response = await llmService.generateResponse(messages);
      expect(response.content).toBeDefined();
      console.log('First exchange:', response.content);

      // Add assistant response and continue conversation
      messages.push({
        id: '3',
        role: 'assistant',
        content: response.content,
        timestamp: new Date().toISOString(),
      });

      messages.push({
        id: '4',
        role: 'user',
        content: 'Specifically, I am not receiving email notifications for new tickets.',
        timestamp: new Date().toISOString(),
      });

      // Second exchange
      response = await llmService.generateResponse(messages);
      expect(response.content).toBeDefined();
      expect(response.content.toLowerCase()).toMatch(/email|notification|ticket/);
      console.log('Second exchange:', response.content);
    }, 90000);
  });
});
