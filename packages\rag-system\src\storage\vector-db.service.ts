import { Pinecone } from '@pinecone-database/pinecone';
import { VectorDocument } from '../types/document';
import { logger } from '../utils/logger';

export interface VectorSearchResult {
  id: string;
  score: number;
  metadata: {
    content: string;
    category: string;
    question: string;
    answer: string;
    source: string;
    lastUpdated: string;
    contentLength: number;
  };
}

export interface VectorSearchOptions {
  topK?: number;
  filter?: Record<string, any>;
  includeMetadata?: boolean;
  includeValues?: boolean;
}

export interface VectorDbConfig {
  apiKey: string;
  environment?: string;
  indexName: string;
  dimension: number;
  metric?: 'cosine' | 'euclidean' | 'dotproduct';
}

/**
 * Pinecone vector database service for storing and retrieving FAQ embeddings
 */
export class VectorDbService {
  private readonly pinecone: Pinecone;
  private readonly indexName: string;
  private readonly dimension: number;

  constructor(config: VectorDbConfig) {
    this.pinecone = new Pinecone({
      apiKey: config.apiKey,
    });
    this.indexName = config.indexName;
    this.dimension = config.dimension;
  }

  /**
   * Initialize the vector database and create index if needed
   */
  async initialize(): Promise<void> {
    try {
      logger.info(`Initializing Pinecone vector database with index: ${this.indexName}`);

      // Check if index exists
      const indexList = await this.pinecone.listIndexes();
      const indexExists = indexList.indexes?.some(index => index.name === this.indexName);

      if (!indexExists) {
        logger.info(`Creating new Pinecone index: ${this.indexName}`);
        await this.pinecone.createIndex({
          name: this.indexName,
          dimension: this.dimension,
          metric: 'cosine',
          spec: {
            serverless: {
              cloud: 'aws',
              region: 'us-east-1',
            },
          },
        });

        // Wait for index to be ready
        await this.waitForIndexReady();
      } else {
        logger.info(`Using existing Pinecone index: ${this.indexName}`);
      }

    } catch (error) {
      logger.error('Error initializing vector database:', error);
      throw error;
    }
  }

  /**
   * Wait for index to be ready
   */
  private async waitForIndexReady(maxWaitTime = 60000): Promise<void> {
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const indexStats = await this.pinecone.index(this.indexName).describeIndexStats();
        if (indexStats) {
          logger.info('Index is ready');
          return;
        }
      } catch (error) {
        // Index might not be ready yet, continue waiting
        logger.debug('Index not ready yet, continuing to wait...', error instanceof Error ? error.message : String(error));
      }

      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    throw new Error(`Index ${this.indexName} did not become ready within ${maxWaitTime}ms`);
  }

  /**
   * Store vector documents in Pinecone
   */
  async upsertVectors(documents: VectorDocument[]): Promise<void> {
    try {
      logger.info(`Upserting ${documents.length} vectors to Pinecone`);

      const index = this.pinecone.index(this.indexName);

      // Prepare vectors for upsert
      const vectors = documents.map(doc => ({
        id: doc.id,
        values: doc.vector,
        metadata: doc.metadata,
      }));

      // Batch upsert (Pinecone recommends batches of 100)
      const batchSize = 100;
      for (let i = 0; i < vectors.length; i += batchSize) {
        const batch = vectors.slice(i, i + batchSize);
        await index.upsert(batch);
        logger.debug(`Upserted batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(vectors.length / batchSize)}`);
      }

      logger.info(`Successfully upserted ${documents.length} vectors`);

    } catch (error) {
      logger.error('Error upserting vectors:', error);
      throw error;
    }
  }

  /**
   * Search for similar vectors
   */
  async searchSimilar(
    queryVector: number[],
    options: VectorSearchOptions = {}
  ): Promise<VectorSearchResult[]> {
    try {
      const {
        topK = 5,
        filter,
        includeMetadata = true,
        includeValues = false,
      } = options;

      logger.debug(`Searching for ${topK} similar vectors`);

      const index = this.pinecone.index(this.indexName);

      const queryResponse = await index.query({
        vector: queryVector,
        topK,
        filter,
        includeMetadata,
        includeValues,
      });

      const results: VectorSearchResult[] = queryResponse.matches?.map(match => ({
        id: match.id,
        score: match.score || 0,
        metadata: match.metadata as VectorSearchResult['metadata'],
      })) || [];

      logger.debug(`Found ${results.length} similar vectors`);
      return results;

    } catch (error) {
      logger.error('Error searching vectors:', error);
      throw error;
    }
  }

  /**
   * Delete vectors by IDs
   */
  async deleteVectors(ids: string[]): Promise<void> {
    try {
      logger.info(`Deleting ${ids.length} vectors from Pinecone`);

      const index = this.pinecone.index(this.indexName);
      await index.deleteMany(ids);

      logger.info(`Successfully deleted ${ids.length} vectors`);

    } catch (error) {
      logger.error('Error deleting vectors:', error);
      throw error;
    }
  }

  /**
   * Delete all vectors (use with caution)
   */
  async deleteAllVectors(): Promise<void> {
    try {
      logger.warn('Deleting ALL vectors from Pinecone index');

      const index = this.pinecone.index(this.indexName);
      await index.deleteAll();

      logger.info('Successfully deleted all vectors');

    } catch (error) {
      logger.error('Error deleting all vectors:', error);
      throw error;
    }
  }

  /**
   * Get index statistics
   */
  async getIndexStats(): Promise<any> {
    try {
      const index = this.pinecone.index(this.indexName);
      const stats = await index.describeIndexStats();

      logger.debug('Retrieved index statistics');
      return stats;

    } catch (error) {
      logger.error('Error getting index stats:', error);
      throw error;
    }
  }

  /**
   * Check if the vector database is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      await this.getIndexStats();
      return true;
    } catch (error) {
      logger.warn('Vector database is not available:', error);
      return false;
    }
  }

  /**
   * Get vector by ID
   */
  async getVector(id: string): Promise<VectorDocument | null> {
    try {
      const index = this.pinecone.index(this.indexName);
      const response = await index.fetch([id]);

      const records = response.records || {};
      const vector = records[id];
      if (!vector) {
        return null;
      }

      return {
        id: vector.id,
        vector: vector.values || [],
        metadata: vector.metadata as VectorDocument['metadata'],
      };

    } catch (error) {
      logger.error(`Error getting vector ${id}:`, error);
      throw error;
    }
  }
}
