import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { ChatService } from '../chat.service';
import { RagEnhancedLlmService } from '../../llm/rag-enhanced-llm.service';
import { LlmService } from '../../llm/llm.service';
import { ToolCallingService } from '../../tool-calling/tool-calling.service';
import { RagService } from '../../rag/rag.service';
import { User } from '@otrs-ai-powered/shared';

describe('RAG-Enhanced Chat Integration', () => {
  let chatService: ChatService;
  let ragEnhancedLlmService: RagEnhancedLlmService;
  let ragService: RagService;
  let module: TestingModule;

  const mockUser: User = {
    id: 'test-user-1',
    username: 'testuser',
    email: '<EMAIL>',
    roles: ['user'],
    permissions: ['chat:read', 'chat:write'],
  };

  beforeAll(async () => {
    module = await Test.createTestingModule({
      providers: [
        ChatService,
        RagEnhancedLlmService,
        {
          provide: LlmService,
          useValue: {
            generateResponse: jest.fn().mockResolvedValue({
              content: 'This is a direct LLM response',
              toolCalls: [],
            }),
            generateCompletion: jest.fn().mockResolvedValue({
              content: 'This is a direct LLM completion',
              toolCalls: [],
            }),
          },
        },
        {
          provide: ToolCallingService,
          useValue: {
            executeToolCalls: jest.fn().mockResolvedValue([]),
          },
        },
        {
          provide: RagService,
          useValue: {
            searchRelevantFaqs: jest.fn(),
            isAvailable: jest.fn().mockResolvedValue(true),
            getStatistics: jest.fn().mockResolvedValue({
              totalFaqEntries: 50,
              vectorDimensions: 1536,
              lastUpdated: new Date().toISOString(),
              indexHealth: 'healthy',
            }),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              const config = {
                EMBEDDING_API_KEY: 'test-key',
                EMBEDDING_MODEL: 'text-embedding-3-small',
                EMBEDDING_DIMENSIONS: '1536',
                PINECONE_API_KEY: 'test-pinecone-key',
                PINECONE_INDEX_NAME: 'test-index',
                RAG_TOP_K: '5',
                RAG_SIMILARITY_THRESHOLD: '0.6',
              };
              return config[key];
            }),
          },
        },
      ],
    }).compile();

    chatService = module.get<ChatService>(ChatService);
    ragEnhancedLlmService = module.get<RagEnhancedLlmService>(RagEnhancedLlmService);
    ragService = module.get<RagService>(RagService);
  });

  afterAll(async () => {
    await module.close();
  });

  describe('Chat Session Management', () => {
    test('should create a new chat session', async () => {
      const session = await chatService.createSession(mockUser);
      
      expect(session).toBeDefined();
      expect(session.id).toBeDefined();
      expect(session.userId).toBe(mockUser.id);
      expect(session.messages).toHaveLength(1);
      expect(session.messages[0].role).toBe('system');
    });

    test('should retrieve existing chat session', async () => {
      const session = await chatService.createSession(mockUser);
      const retrievedSession = await chatService.getSession(session.id, mockUser.id);
      
      expect(retrievedSession).toEqual(session);
    });
  });

  describe('RAG-Enhanced Message Processing', () => {
    test('should use RAG for FAQ-related questions', async () => {
      // Mock RAG service to return relevant FAQ results
      const mockFaqResults = [
        {
          faqEntry: {
            id: 'faq-1',
            question: 'How do I reset my password?',
            answer: 'To reset your password, go to the login page and click "Forgot Password".',
            category: 'Authentication',
          },
          similarityScore: 0.85,
          relevanceRank: 1,
        },
      ];

      jest.spyOn(ragService, 'searchRelevantFaqs').mockResolvedValue(mockFaqResults);

      const session = await chatService.createSession(mockUser);
      
      const response = await chatService.sendMessage(mockUser, {
        sessionId: session.id,
        content: 'How can I reset my password?',
      });

      expect(response).toBeDefined();
      expect(response.role).toBe('assistant');
      expect(response.metadata?.usedRag).toBe(true);
      expect(response.metadata?.ragResultsCount).toBe(1);
      expect(ragService.searchRelevantFaqs).toHaveBeenCalledWith('How can I reset my password?', 3);
    });

    test('should fallback to direct LLM for non-FAQ questions', async () => {
      // Mock RAG service to return no relevant results
      jest.spyOn(ragService, 'searchRelevantFaqs').mockResolvedValue([]);

      const session = await chatService.createSession(mockUser);
      
      const response = await chatService.sendMessage(mockUser, {
        sessionId: session.id,
        content: 'What is the weather like today?',
      });

      expect(response).toBeDefined();
      expect(response.role).toBe('assistant');
      expect(response.metadata?.usedRag).toBe(false);
      expect(response.metadata?.fallbackReason).toBeDefined();
    });

    test('should handle RAG service errors gracefully', async () => {
      // Mock RAG service to throw an error
      jest.spyOn(ragService, 'searchRelevantFaqs').mockRejectedValue(new Error('RAG service error'));

      const session = await chatService.createSession(mockUser);
      
      const response = await chatService.sendMessage(mockUser, {
        sessionId: session.id,
        content: 'How do I configure email settings?',
      });

      expect(response).toBeDefined();
      expect(response.role).toBe('assistant');
      expect(response.metadata?.usedRag).toBe(false);
      expect(response.metadata?.fallbackReason).toContain('RAG error');
    });
  });

  describe('RAG Service Integration', () => {
    test('should search FAQs directly', async () => {
      const mockResults = [
        {
          faqEntry: {
            id: 'faq-1',
            question: 'Test question',
            answer: 'Test answer',
            category: 'Test',
          },
          similarityScore: 0.9,
          relevanceRank: 1,
        },
      ];

      jest.spyOn(ragService, 'searchRelevantFaqs').mockResolvedValue(mockResults);

      const results = await chatService.searchFaqs('test query');
      
      expect(results).toEqual(mockResults);
      expect(ragService.searchRelevantFaqs).toHaveBeenCalledWith('test query', 5);
    });

    test('should get RAG statistics', async () => {
      const stats = await chatService.getRagStatistics();
      
      expect(stats).toBeDefined();
      expect(stats.totalFaqEntries).toBe(50);
      expect(stats.vectorDimensions).toBe(1536);
      expect(stats.indexHealth).toBe('healthy');
    });

    test('should check RAG availability', async () => {
      const isAvailable = await chatService.isRagAvailable();
      
      expect(isAvailable).toBe(true);
      expect(ragService.isAvailable).toHaveBeenCalled();
    });
  });

  describe('Message Flow with RAG Context', () => {
    test('should maintain conversation context with RAG responses', async () => {
      const mockFaqResults = [
        {
          faqEntry: {
            id: 'faq-email',
            question: 'How do I configure email settings?',
            answer: 'Go to Settings > Email Configuration and enter your SMTP details.',
            category: 'Email',
          },
          similarityScore: 0.8,
          relevanceRank: 1,
        },
      ];

      jest.spyOn(ragService, 'searchRelevantFaqs').mockResolvedValue(mockFaqResults);

      const session = await chatService.createSession(mockUser);
      
      // First message with RAG
      const firstResponse = await chatService.sendMessage(mockUser, {
        sessionId: session.id,
        content: 'How do I set up email?',
      });

      expect(firstResponse.metadata?.usedRag).toBe(true);

      // Second message should have access to conversation history
      const secondResponse = await chatService.sendMessage(mockUser, {
        sessionId: session.id,
        content: 'Can you provide more details about SMTP?',
      });

      expect(secondResponse).toBeDefined();
      
      // Verify session has accumulated messages
      const updatedSession = await chatService.getSession(session.id, mockUser.id);
      expect(updatedSession.messages.length).toBeGreaterThan(3); // system + 2 user + 2 assistant
    });

    test('should handle mixed RAG and tool calling scenarios', async () => {
      // Mock tool calls in LLM response
      const mockLlmResponse = {
        content: 'I need to create a ticket for you.',
        toolCalls: [
          {
            toolId: 'tool-1',
            toolName: 'create_ticket',
            arguments: { title: 'Test ticket', description: 'Test description' },
          },
        ],
      };

      jest.spyOn(ragService, 'searchRelevantFaqs').mockResolvedValue([]); // No RAG results
      const llmService = module.get<LlmService>(LlmService);
      jest.spyOn(llmService, 'generateResponse').mockResolvedValue(mockLlmResponse);

      const toolCallingService = module.get<ToolCallingService>(ToolCallingService);
      jest.spyOn(toolCallingService, 'executeToolCalls').mockResolvedValue([
        {
          toolCallId: 'tool-1',
          result: { success: true, ticketId: 'TICKET-123' },
          error: null,
        },
      ]);

      const session = await chatService.createSession(mockUser);
      
      const response = await chatService.sendMessage(mockUser, {
        sessionId: session.id,
        content: 'Please create a support ticket for me',
      });

      expect(response).toBeDefined();
      expect(response.metadata?.hasToolCalls).toBe(true);
      expect(response.metadata?.usedRag).toBe(false);
    });
  });
});
