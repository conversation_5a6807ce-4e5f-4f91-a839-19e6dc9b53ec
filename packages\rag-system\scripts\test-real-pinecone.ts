#!/usr/bin/env ts-node

import { RagService, RagConfig } from '../src/retrieval/retriever.service';
import { logger } from '../src/utils/logger';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.test') });

async function main() {
  try {
    logger.info('🚀 Starting Real Pinecone Integration Test...');

    // Configuration with real Pinecone API key
    const config: RagConfig = {
      embedding: {
        provider: (process.env.EMBEDDING_PROVIDER as 'openai' | 'vllm') || 'vllm',
        apiKey: process.env.EMBEDDING_API_KEY || 'test-key',
        model: process.env.EMBEDDING_MODEL || 'microsoft/Phi-4-mini-instruct',
        baseUrl: process.env.EMBEDDING_BASE_URL,
        dimensions: parseInt(process.env.EMBEDDING_DIMENSIONS || '1536'),
        batchSize: 50,
        timeout: 30000,
      },
      vectorDb: {
        apiKey: process.env.PINECONE_API_KEY || '',
        indexName: process.env.PINECONE_INDEX_NAME || 'otrs-faq-test',
        dimension: 1536,
        metric: 'cosine',
      },
      retrieval: {
        topK: 5,
        similarityThreshold: 0.6,
        maxContextLength: 4000,
        enableReranking: true,
      },
    };

    const ragService = new RagService(config);

    // Test 1: Initialize RAG Service
    logger.info('\n📋 Test 1: Initializing RAG Service...');
    await ragService.initialize();
    logger.info('✅ RAG Service initialized successfully');

    // Test 2: Check Service Availability
    logger.info('\n📋 Test 2: Checking Service Availability...');
    const isAvailable = await ragService.isAvailable();
    logger.info(`✅ Service Available: ${isAvailable}`);

    // Test 3: Get Service Statistics
    logger.info('\n📋 Test 3: Getting Service Statistics...');
    const stats = await ragService.getStatistics();
    logger.info('✅ Service Statistics:', JSON.stringify(stats, null, 2));

    // Test 4: Index FAQ Data (if embedding service is available)
    if (process.env.EMBEDDING_API_KEY && process.env.EMBEDDING_API_KEY !== 'test-key') {
      logger.info('\n📋 Test 4: Indexing FAQ Data...');
      try {
        const indexingResult = await ragService.indexFaqData();
        logger.info('✅ FAQ Data Indexed Successfully:');
        logger.info(`   - Total Documents: ${indexingResult.totalDocuments}`);
        logger.info(`   - Successfully Indexed: ${indexingResult.successfullyIndexed}`);
        logger.info(`   - Failed: ${indexingResult.failed}`);
        logger.info(`   - Indexing Time: ${indexingResult.indexingTime}ms`);
        logger.info(`   - Total Tokens Used: ${indexingResult.statistics.totalTokensUsed}`);
        logger.info(`   - Categories: ${indexingResult.statistics.categories.join(', ')}`);
      } catch (error) {
        logger.warn('⚠️ FAQ Data Indexing failed (expected without OpenAI API key):', error instanceof Error ? error.message : String(error));
      }
    } else {
      logger.info('\n📋 Test 4: Skipping FAQ Data Indexing (no OpenAI API key)');
    }

    // Test 5: Search FAQs (if data is indexed)
    if (process.env.EMBEDDING_API_KEY && process.env.EMBEDDING_API_KEY !== 'test-key') {
      logger.info('\n📋 Test 5: Testing FAQ Search...');
      
      const testQueries = [
        'How do I configure email settings?',
        'Password reset procedure',
        'SharePoint file access issues',
        'Teams collaboration setup',
        'MFA authentication problems',
      ];

      for (const query of testQueries) {
        try {
          logger.info(`\n🔍 Searching: "${query}"`);
          const searchResult = await ragService.searchFaqs(query, {
            topK: 3,
            similarityThreshold: 0.5,
          });

          logger.info(`   Results: ${searchResult.results.length} found in ${searchResult.searchTime}ms`);
          
          if (searchResult.results.length > 0) {
            const topResult = searchResult.results[0];
            logger.info(`   Top Result (Score: ${topResult.score.toFixed(3)}):`);
            logger.info(`     Category: ${topResult.category}`);
            logger.info(`     Question: ${topResult.question}`);
            logger.info(`     Answer: ${topResult.answer.substring(0, 100)}...`);
          }
        } catch (error) {
          logger.warn(`   ⚠️ Search failed for "${query}":`, error instanceof Error ? error.message : String(error));
        }
      }
    } else {
      logger.info('\n📋 Test 5: Skipping FAQ Search (no OpenAI API key)');
    }

    // Test 6: Test Hybrid Search
    if (process.env.EMBEDDING_API_KEY && process.env.EMBEDDING_API_KEY !== 'test-key') {
      logger.info('\n📋 Test 6: Testing Hybrid Search...');
      try {
        const hybridResult = await ragService.hybridSearch(
          'email security',
          { category: 'Outlook & Email Security' },
          3
        );

        logger.info(`   Hybrid Search Results: ${hybridResult.results.length} found`);
        hybridResult.results.forEach((result, index) => {
          logger.info(`     ${index + 1}. ${result.question} (Score: ${result.score.toFixed(3)})`);
        });
      } catch (error) {
        logger.warn('   ⚠️ Hybrid search failed:', error instanceof Error ? error.message : String(error));
      }
    } else {
      logger.info('\n📋 Test 6: Skipping Hybrid Search (no OpenAI API key)');
    }

    // Test 7: Get Categories
    logger.info('\n📋 Test 7: Getting Available Categories...');
    const categories = await ragService.getCategories();
    logger.info(`✅ Available Categories (${categories.length}):`, categories.join(', '));

    // Test 8: Performance Benchmark
    if (process.env.EMBEDDING_API_KEY && process.env.EMBEDDING_API_KEY !== 'test-key') {
      logger.info('\n📋 Test 8: Performance Benchmark...');
      const benchmarkQueries = [
        'email configuration',
        'password reset',
        'file sharing',
      ];

      const performanceResults = [];
      
      for (const query of benchmarkQueries) {
        try {
          const startTime = Date.now();
          const result = await ragService.searchFaqs(query, { topK: 5 });
          const responseTime = Date.now() - startTime;
          
          performanceResults.push({
            query,
            responseTime,
            resultCount: result.results.length,
            topScore: result.results[0]?.score || 0,
          });
          
          logger.info(`   "${query}": ${responseTime}ms, ${result.results.length} results, top score: ${(result.results[0]?.score || 0).toFixed(3)}`);
        } catch (error) {
          logger.warn(`   ⚠️ Benchmark failed for "${query}":`, error instanceof Error ? error.message : String(error));
        }
      }

      if (performanceResults.length > 0) {
        const avgResponseTime = performanceResults.reduce((sum, r) => sum + r.responseTime, 0) / performanceResults.length;
        const avgResultCount = performanceResults.reduce((sum, r) => sum + r.resultCount, 0) / performanceResults.length;
        const avgTopScore = performanceResults.reduce((sum, r) => sum + r.topScore, 0) / performanceResults.length;
        
        logger.info(`\n📊 Performance Summary:`);
        logger.info(`   Average Response Time: ${avgResponseTime.toFixed(0)}ms`);
        logger.info(`   Average Result Count: ${avgResultCount.toFixed(1)}`);
        logger.info(`   Average Top Score: ${avgTopScore.toFixed(3)}`);
      }
    } else {
      logger.info('\n📋 Test 8: Skipping Performance Benchmark (no OpenAI API key)');
    }

    logger.info('\n🎉 Real Pinecone Integration Test Complete!');
    logger.info('\n📝 Summary:');
    logger.info('   ✅ Pinecone vector database connection established');
    logger.info('   ✅ RAG service initialization successful');
    logger.info('   ✅ Service availability confirmed');
    
    if (process.env.EMBEDDING_API_KEY && process.env.EMBEDDING_API_KEY !== 'test-key') {
      logger.info('   ✅ FAQ data indexing and search functionality validated');
      logger.info('   ✅ Performance benchmarks completed');
    } else {
      logger.info('   ⚠️ Full testing requires OpenAI API key for embeddings');
    }

  } catch (error) {
    logger.error('❌ Real Pinecone Integration Test Failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
