import express from 'express';
import dotenv from 'dotenv';
import { EmbeddingService } from './embeddings/embedding.service';
import { VectorDbService } from './storage/vector-db.service';
import { RagService, RagConfig } from './retrieval/retriever.service';
import { IndexerService } from './indexing/indexer.service';
import { logger } from './utils/logger';

// Load environment variables
dotenv.config();

// Create configuration
const ragConfig: RagConfig = {
  embedding: {
    provider: 'openai',
    apiKey: process.env.EMBEDDING_API_KEY || 'test-key',
    model: process.env.EMBEDDING_MODEL || 'text-embedding-3-small',
    dimensions: parseInt(process.env.EMBEDDING_DIMENSIONS || '1536'),
    batchSize: parseInt(process.env.EMBEDDING_BATCH_SIZE || '100'),
    timeout: 30000,
  },
  vectorDb: {
    apiKey: process.env.PINECONE_API_KEY || '',
    indexName: process.env.PINECONE_INDEX_NAME || 'otrs-faq',
    dimension: parseInt(process.env.PINECONE_DIMENSION || '1536'),
    metric: 'cosine',
  },
  retrieval: {
    topK: parseInt(process.env.RAG_TOP_K || '5'),
    similarityThreshold: parseFloat(process.env.RAG_SIMILARITY_THRESHOLD || '0.7'),
    maxContextLength: parseInt(process.env.RAG_MAX_CONTEXT_LENGTH || '4000'),
    enableReranking: process.env.RAG_ENABLE_RERANKING === 'true',
  },
};

// Initialize services
const embeddingService = new EmbeddingService(ragConfig.embedding);
const vectorDbService = new VectorDbService(ragConfig.vectorDb);
const ragService = new RagService(ragConfig);
const indexerService = new IndexerService(vectorDbService, embeddingService);

// Create Express app
const app = express();
const port = process.env.PORT || 6000;

// Middleware
app.use(express.json());

// API endpoints
app.post('/api/query', async (req, res) => {
  try {
    const { query, topK = 5, category } = req.body;

    if (!query) {
      return res.status(400).json({ error: 'Query is required' });
    }

    logger.info(`Processing query: ${query}`);
    const results = await ragService.searchFaqs(query, { topK, category });

    return res.json({ results });
  } catch (error) {
    logger.error('Error processing query:', error);
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while processing the query';
    return res.status(500).json({
      error: errorMessage
    });
  }
});

app.post('/api/index', async (req, res) => {
  try {
    const { documents } = req.body;

    if (!documents || !Array.isArray(documents)) {
      return res.status(400).json({ error: 'Documents array is required' });
    }

    logger.info(`Indexing ${documents.length} documents`);
    const result = await indexerService.indexDocuments(documents);

    return res.json({ success: true, count: result.count });
  } catch (error) {
    logger.error('Error indexing documents:', error);
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while indexing documents';
    return res.status(500).json({
      error: errorMessage
    });
  }
});

app.get('/health', (req, res) => {
  res.json({ status: 'ok' });
});

// Start the server
if (process.env.NODE_ENV !== 'test') {
  app.listen(port, () => {
    logger.info(`RAG System running on port ${port}`);
  });
}

export {
  embeddingService,
  vectorDbService,
  ragService,
  indexerService
};

// Export types and classes for external use
export { RagService } from './retrieval/retriever.service';
export type { RagConfig } from './retrieval/retriever.service';
export { EmbeddingService } from './embeddings/embedding.service';
export type { EmbeddingConfig } from './embeddings/embedding.service';
export { VectorDbService } from './storage/vector-db.service';
export type { VectorDbConfig } from './storage/vector-db.service';
export { dataProcessor } from './indexing/data-processor';
export * from './types/document';
