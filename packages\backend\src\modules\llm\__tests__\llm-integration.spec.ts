import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { LlmService } from '../llm.service';
import { LlmProviderFactory } from '../providers/llm-provider.factory';
import { VllmProvider } from '../providers/vllm.provider';
import { ChatMessage, Tool } from '@otrs-ai-powered/shared';
import { LlmProviderType } from '../interfaces/llm-provider.interface';

// Mock fetch globally
global.fetch = jest.fn();

describe('LLM Integration with vLLM Provider', () => {
  let llmService: LlmService;
  let configService: ConfigService;
  let mockFetch: jest.MockedFunction<typeof fetch>;

  const mockVllmResponse = {
    id: 'chatcmpl-test',
    object: 'chat.completion',
    created: Date.now(),
    model: 'microsoft/Phi-4-mini-instruct',
    choices: [
      {
        index: 0,
        message: {
          role: 'assistant' as const,
          content: 'Hello! I am an AI assistant for OTRS. How can I help you today?',
        },
        finish_reason: 'stop',
      },
    ],
    usage: {
      prompt_tokens: 25,
      completion_tokens: 15,
      total_tokens: 40,
    },
  };

  beforeEach(async () => {
    mockFetch = fetch as jest.MockedFunction<typeof fetch>;
    mockFetch.mockClear();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LlmService,
        LlmProviderFactory,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: any) => {
              const config: Record<string, any> = {
                'llm.provider': 'vllm',
                'llm.apiKey': 'token-abc123-must-long-enough',
                'llm.model': 'microsoft/Phi-4-mini-instruct',
                'llm.temperature': 0.7,
                'llm.maxTokens': 2000,
                'llm.timeout': 30000,
                'llm.baseUrl': 'https://antelope-premium-intensely.ngrok-free.app',
              };
              return config[key] ?? defaultValue;
            }),
          },
        },
      ],
    }).compile();

    llmService = module.get<LlmService>(LlmService);
    configService = module.get<ConfigService>(ConfigService);

    // Mock successful validation
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => mockVllmResponse,
    } as Response);

    // Initialize the service
    await llmService.onModuleInit();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Service Initialization', () => {
    it('should initialize with vLLM provider', async () => {
      expect(llmService).toBeDefined();
      expect(await llmService.isAvailable()).toBe(true);
      expect(llmService.getProviderInfo().name).toBe('vllm');
      expect(llmService.getProviderInfo().model).toBe('microsoft/Phi-4-mini-instruct');
    });

    it('should validate vLLM configuration on startup', async () => {
      // Validation call should have been made during initialization
      expect(mockFetch).toHaveBeenCalled();
    });
  });

  describe('Message Generation', () => {
    it('should generate response for simple conversation', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockVllmResponse,
      } as Response);

      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'system',
          content: 'You are an AI assistant for OTRS.',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          role: 'user',
          content: 'Hello, I need help with my ticket.',
          timestamp: new Date().toISOString(),
        },
      ];

      const response = await llmService.generateResponse(messages);

      expect(response.content).toBe('Hello! I am an AI assistant for OTRS. How can I help you today?');
      expect(response.toolCalls).toBeUndefined();
    });

    it('should handle tool calling scenario', async () => {
      const responseWithTools = {
        ...mockVllmResponse,
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant' as const,
              content: 'I will create a ticket for you.',
              tool_calls: [
                {
                  id: 'call_123',
                  type: 'function' as const,
                  function: {
                    name: 'create_ticket',
                    arguments: '{"title": "Help Request", "description": "User needs assistance"}',
                  },
                },
              ],
            },
            finish_reason: 'tool_calls',
          },
        ],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => responseWithTools,
      } as Response);

      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'user',
          content: 'Please create a ticket for my issue.',
          timestamp: new Date().toISOString(),
        },
      ];

      const tools: Tool[] = [
        {
          type: 'function',
          name: 'create_ticket',
          description: 'Create a new OTRS ticket',
          parameters: {
            type: 'object',
            properties: {
              title: { type: 'string', description: 'Ticket title' },
              description: { type: 'string', description: 'Ticket description' },
            },
            required: ['title', 'description'],
          },
        },
      ];

      const response = await llmService.generateResponse(messages, tools);

      expect(response.content).toBe('I will create a ticket for you.');
      expect(response.toolCalls).toHaveLength(1);
      expect(response.toolCalls![0].toolName).toBe('create_ticket');
      expect(response.toolCalls![0].arguments).toEqual({
        title: 'Help Request',
        description: 'User needs assistance',
      });
    });

    it('should handle errors gracefully', async () => {
      // Clear any previous mocks and reject all calls
      mockFetch.mockClear();
      mockFetch.mockRejectedValue(new Error('Network error'));

      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'user',
          content: 'Hello',
          timestamp: new Date().toISOString(),
        },
      ];

      const response = await llmService.generateResponse(messages);

      // Should return fallback message
      expect(response.content).toContain('error processing your request');
      expect(response.toolCalls).toBeUndefined();
    });
  });

  describe('Provider Switching', () => {
    it('should allow switching to different provider configuration', async () => {
      const newConfig = {
        apiKey: 'new-api-key',
        model: 'microsoft/Phi-4-mini-instruct',
        baseUrl: 'http://localhost:8001',
        temperature: 0.5,
        maxTokens: 1000,
        timeout: 20000,
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockVllmResponse,
      } as Response);

      await llmService.switchProvider(LlmProviderType.VLLM, newConfig);

      expect(llmService.getProviderInfo().model).toBe('microsoft/Phi-4-mini-instruct');
    });

    it('should validate new provider configuration', async () => {
      const invalidConfig = {
        apiKey: 'invalid-key',
        model: 'microsoft/Phi-4-mini-instruct',
        baseUrl: 'http://localhost:8001',
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        text: async () => 'Invalid API key',
      } as Response);

      await expect(
        llmService.switchProvider(LlmProviderType.VLLM, invalidConfig)
      ).rejects.toThrow();
    });
  });

  describe('Chat Format Validation', () => {
    it('should properly format Phi-4 chat messages', async () => {
      let capturedRequest: any;
      
      mockFetch.mockImplementationOnce(async (url, options) => {
        capturedRequest = JSON.parse(options?.body as string);
        return {
          ok: true,
          json: async () => mockVllmResponse,
        } as Response;
      });

      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'system',
          content: 'You are a helpful assistant.',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          role: 'user',
          content: 'What is OTRS?',
          timestamp: new Date().toISOString(),
        },
      ];

      await llmService.generateResponse(messages);

      expect(capturedRequest).toBeDefined();
      expect(capturedRequest.model).toBe('microsoft/Phi-4-mini-instruct');
      expect(capturedRequest.messages).toHaveLength(1);
      expect(capturedRequest.messages[0].content).toContain('<|system|>');
      expect(capturedRequest.messages[0].content).toContain('<|user|>');
      expect(capturedRequest.messages[0].content).toContain('<|assistant|>');
    });

    it('should include tools in MCP format when provided', async () => {
      let capturedRequest: any;
      
      mockFetch.mockImplementationOnce(async (url, options) => {
        capturedRequest = JSON.parse(options?.body as string);
        return {
          ok: true,
          json: async () => mockVllmResponse,
        } as Response;
      });

      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'user',
          content: 'Create a ticket for me',
          timestamp: new Date().toISOString(),
        },
      ];

      const tools: Tool[] = [
        {
          type: 'function',
          name: 'create_ticket',
          description: 'Create a new ticket',
          parameters: { type: 'object', properties: {} },
        },
      ];

      await llmService.generateResponse(messages, tools);

      expect(capturedRequest.messages[0].content).toContain('<|tool|>');
      expect(capturedRequest.messages[0].content).toContain('create_ticket');
      expect(capturedRequest.messages[0].content).toContain('<|/tool|>');
    });
  });
});
