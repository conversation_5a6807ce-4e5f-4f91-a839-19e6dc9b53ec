import { EmbeddingService, EmbeddingConfig } from '../embeddings/embedding.service';

describe('EmbeddingService', () => {
  let embeddingService: EmbeddingService;
  let config: EmbeddingConfig;

  beforeAll(() => {
    config = {
      provider: 'vllm',
      apiKey: process.env.EMBEDDING_API_KEY || 'test-key',
      model: process.env.EMBEDDING_MODEL || 'sentence-transformers/all-MiniLM-L6-v2',
      baseUrl: process.env.EMBEDDING_BASE_URL,
      dimensions: parseInt(process.env.EMBEDDING_DIMENSIONS || '384'),
      batchSize: 10,
      timeout: 30000,
    };
    embeddingService = new EmbeddingService(config);
  });

  describe('Service Information', () => {
    test('should return correct service info', () => {
      const serviceInfo = embeddingService.getServiceInfo();
      
      expect(serviceInfo).toHaveProperty('provider', config.provider);
      expect(serviceInfo).toHaveProperty('model', config.model);
      expect(serviceInfo).toHaveProperty('dimensions', config.dimensions);
    });

    test('should return correct dimensions', () => {
      const dimensions = embeddingService.getDimensions();
      expect(dimensions).toBe(config.dimensions);
    });
  });

  describe('Single Embedding Generation', () => {
    test('should generate embedding for text', async () => {
      const testText = 'How do I configure email settings in OTRS?';
      
      const result = await embeddingService.generateEmbedding(testText);
      
      expect(result).toHaveProperty('embedding');
      expect(result).toHaveProperty('tokenCount');
      expect(result).toHaveProperty('model');
      expect(result.embedding).toHaveLength(config.dimensions);
      expect(result.model).toBe(config.model);
      expect(result.tokenCount).toBeGreaterThan(0);
    }, 30000);

    test('should handle empty text gracefully', async () => {
      await expect(embeddingService.generateEmbedding('')).rejects.toThrow('Input text cannot be empty or contain only whitespace');
      await expect(embeddingService.generateEmbedding('   ')).rejects.toThrow('Input text cannot be empty or contain only whitespace');
      await expect(embeddingService.generateEmbedding('\n\t  ')).rejects.toThrow('Input text cannot be empty or contain only whitespace');
    });

    test('should handle very long text', async () => {
      const longText = 'This is a very long text that exceeds the typical token limits for embedding models. '.repeat(100);

      // The text should be automatically truncated to fit within token limits
      const result = await embeddingService.generateEmbedding(longText);

      expect(result.embedding).toHaveLength(config.dimensions);
      expect(result.tokenCount).toBeGreaterThan(0);
      expect(result.tokenCount).toBeLessThanOrEqual(256); // Should be within token limit
    }, 30000);

    test('should handle text with special characters and whitespace', async () => {
      const messyText = '  \n\t  How do I configure   email\r\n\tsettings?  \n  ';

      const result = await embeddingService.generateEmbedding(messyText);

      expect(result.embedding).toHaveLength(config.dimensions);
      expect(result.tokenCount).toBeGreaterThan(0);
    });
  });

  describe('Batch Embedding Generation', () => {
    test('should generate embeddings for multiple texts', async () => {
      const testTexts = [
        'Email configuration help',
        'SharePoint file access',
        'Teams meeting setup',
        'Password reset guide',
        'MFA authentication',
      ];

      const batchResult = await embeddingService.generateBatchEmbeddings(testTexts);
      
      expect(batchResult).toHaveProperty('embeddings');
      expect(batchResult).toHaveProperty('processedCount');
      expect(batchResult).toHaveProperty('failedCount');
      expect(batchResult).toHaveProperty('totalTokens');
      expect(batchResult.embeddings).toHaveLength(testTexts.length);
      expect(batchResult.processedCount).toBe(testTexts.length);
      expect(batchResult.failedCount).toBe(0);
      
      // Check each embedding
      batchResult.embeddings.forEach(embedding => {
        expect(embedding).toHaveLength(config.dimensions);
      });
    }, 45000);

    test('should handle empty batch', async () => {
      const batchResult = await embeddingService.generateBatchEmbeddings([]);
      
      expect(batchResult.embeddings).toHaveLength(0);
      expect(batchResult.processedCount).toBe(0);
      expect(batchResult.failedCount).toBe(0);
    });
  });

  describe('Semantic Quality', () => {
    test('should show good semantic discrimination', async () => {
      const emailText = 'How do I configure email settings?';
      const emailRelated = 'Email configuration and setup';
      const unrelatedText = 'Cooking recipes and food';

      const [emailEmbedding, emailRelatedEmbedding, unrelatedEmbedding] = await Promise.all([
        embeddingService.generateEmbedding(emailText),
        embeddingService.generateEmbedding(emailRelated),
        embeddingService.generateEmbedding(unrelatedText),
      ]);

      const emailToEmailSim = calculateCosineSimilarity(
        emailEmbedding.embedding, 
        emailRelatedEmbedding.embedding
      );
      const emailToUnrelatedSim = calculateCosineSimilarity(
        emailEmbedding.embedding, 
        unrelatedEmbedding.embedding
      );

      // Email-related texts should be more similar than unrelated texts
      expect(emailToEmailSim).toBeGreaterThan(emailToUnrelatedSim);
      
      // Good discrimination means significant difference
      const discrimination = emailToEmailSim - emailToUnrelatedSim;
      expect(discrimination).toBeGreaterThan(0.3);
      
      // Email-related should have high similarity
      expect(emailToEmailSim).toBeGreaterThan(0.6);
      
      // Unrelated should have lower similarity
      expect(emailToUnrelatedSim).toBeLessThan(0.5);
    }, 45000);
  });

  describe('Service Availability', () => {
    test('should check service availability', async () => {
      const isAvailable = await embeddingService.isAvailable();
      expect(typeof isAvailable).toBe('boolean');
    }, 30000);
  });
});

/**
 * Calculate cosine similarity between two vectors
 */
function calculateCosineSimilarity(a: number[], b: number[]): number {
  if (a.length !== b.length) {
    throw new Error('Vectors must have the same dimensions');
  }

  let dotProduct = 0;
  let normA = 0;
  let normB = 0;

  for (let i = 0; i < a.length; i++) {
    dotProduct += a[i] * b[i];
    normA += a[i] * a[i];
    normB += b[i] * b[i];
  }

  normA = Math.sqrt(normA);
  normB = Math.sqrt(normB);

  if (normA === 0 || normB === 0) {
    return 0;
  }

  return dotProduct / (normA * normB);
}
