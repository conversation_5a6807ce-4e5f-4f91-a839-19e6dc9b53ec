import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Query, 
  HttpException, 
  HttpStatus,
  Logger 
} from '@nestjs/common';
import { RagService } from './rag.service';
import { 
  FaqEntry, 
  RelevantFaqResult, 
  RagStatistics 
} from './interfaces/rag.interface';

export class SearchFaqsDto {
  query: string;
  topK?: number;
  category?: string;
}

export class HybridSearchDto {
  query: string;
  filters?: Record<string, any>;
  topK?: number;
}

export class InitializeRagDto {
  faqData?: FaqEntry[];
}

@Controller('rag')
export class RagController {
  private readonly logger = new Logger(RagController.name);

  constructor(private readonly ragService: RagService) {}

  /**
   * Search FAQs using semantic similarity
   */
  @Post('search')
  async searchFaqs(@Body() searchDto: SearchFaqsDto): Promise<{
    query: string;
    results: RelevantFaqResult[];
    totalResults: number;
  }> {
    try {
      this.logger.log(`FAQ search request: "${searchDto.query}"`);

      if (!searchDto.query || searchDto.query.trim().length === 0) {
        throw new HttpException('Query is required', HttpStatus.BAD_REQUEST);
      }

      const results = await this.ragService.searchRelevantFaqs(
        searchDto.query.trim(),
        searchDto.topK || 5
      );

      return {
        query: searchDto.query,
        results,
        totalResults: results.length,
      };

    } catch (error) {
      this.logger.error('Error in FAQ search:', error);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Failed to search FAQs',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Perform hybrid search with filters
   */
  @Post('hybrid-search')
  async hybridSearch(@Body() searchDto: HybridSearchDto): Promise<{
    query: string;
    results: RelevantFaqResult[];
    totalResults: number;
    filters: Record<string, any>;
  }> {
    try {
      this.logger.log(`Hybrid search request: "${searchDto.query}"`);

      if (!searchDto.query || searchDto.query.trim().length === 0) {
        throw new HttpException('Query is required', HttpStatus.BAD_REQUEST);
      }

      const results = await this.ragService.hybridSearch(
        searchDto.query.trim(),
        searchDto.filters || {},
        searchDto.topK || 5
      );

      return {
        query: searchDto.query,
        results,
        totalResults: results.length,
        filters: searchDto.filters || {},
      };

    } catch (error) {
      this.logger.error('Error in hybrid search:', error);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Failed to perform hybrid search',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Get available FAQ categories
   */
  @Get('categories')
  async getCategories(): Promise<{ categories: string[] }> {
    try {
      const categories = await this.ragService.getCategories();
      return { categories };

    } catch (error) {
      this.logger.error('Error getting categories:', error);
      throw new HttpException(
        'Failed to get categories',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Get RAG system statistics
   */
  @Get('statistics')
  async getStatistics(): Promise<RagStatistics> {
    try {
      return await this.ragService.getStatistics();

    } catch (error) {
      this.logger.error('Error getting statistics:', error);
      throw new HttpException(
        'Failed to get statistics',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Check RAG service availability
   */
  @Get('health')
  async healthCheck(): Promise<{ 
    status: 'healthy' | 'unhealthy';
    isAvailable: boolean;
    timestamp: string;
  }> {
    try {
      const isAvailable = await this.ragService.isAvailable();
      
      return {
        status: isAvailable ? 'healthy' : 'unhealthy',
        isAvailable,
        timestamp: new Date().toISOString(),
      };

    } catch (error) {
      this.logger.error('Error in health check:', error);
      return {
        status: 'unhealthy',
        isAvailable: false,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Initialize RAG service with FAQ data
   */
  @Post('initialize')
  async initialize(@Body() initDto: InitializeRagDto): Promise<{
    message: string;
    initialized: boolean;
    timestamp: string;
  }> {
    try {
      this.logger.log('RAG service initialization requested');

      await this.ragService.initializeWithFaqData(initDto.faqData || []);

      return {
        message: 'RAG service initialized successfully',
        initialized: true,
        timestamp: new Date().toISOString(),
      };

    } catch (error) {
      this.logger.error('Error initializing RAG service:', error);
      throw new HttpException(
        'Failed to initialize RAG service',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Simple FAQ search endpoint for quick queries
   */
  @Get('search')
  async quickSearch(
    @Query('q') query: string,
    @Query('limit') limit?: string,
    @Query('category') category?: string
  ): Promise<{
    query: string;
    results: RelevantFaqResult[];
    totalResults: number;
  }> {
    try {
      if (!query || query.trim().length === 0) {
        throw new HttpException('Query parameter "q" is required', HttpStatus.BAD_REQUEST);
      }

      const topK = limit ? parseInt(limit) : 5;
      if (isNaN(topK) || topK < 1 || topK > 20) {
        throw new HttpException('Limit must be between 1 and 20', HttpStatus.BAD_REQUEST);
      }

      let results: RelevantFaqResult[];

      if (category) {
        // Use hybrid search with category filter
        results = await this.ragService.hybridSearch(
          query.trim(),
          { category },
          topK
        );
      } else {
        // Use regular semantic search
        results = await this.ragService.searchRelevantFaqs(
          query.trim(),
          topK
        );
      }

      return {
        query: query.trim(),
        results,
        totalResults: results.length,
      };

    } catch (error) {
      this.logger.error('Error in quick search:', error);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Failed to search FAQs',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
