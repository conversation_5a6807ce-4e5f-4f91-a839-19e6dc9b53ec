import { Injectable, Logger } from '@nestjs/common';
import { ChatMessage } from '@otrs-ai-powered/shared';
import { LlmService } from './llm.service';
import { RagService } from '../rag/rag.service';
import { RelevantFaqResult } from '../rag/interfaces/rag.interface';


export interface RagEnhancedResponse {
  response: string;
  usedRag: boolean;
  ragResults?: RelevantFaqResult[];
  ragContext?: string;
  fallbackReason?: string;
}

@Injectable()
export class RagEnhancedLlmService {
  private readonly logger = new Logger(RagEnhancedLlmService.name);

  constructor(
    private readonly llmService: LlmService,
    private readonly ragService: RagService,
  ) {}

  /**
   * Generate response with RAG enhancement
   */
  async generateResponseWithRag(
    messages: ChatMessage[],
    options: {
      useRag?: boolean;
      ragThreshold?: number;
      maxRagResults?: number;
      fallbackToLlm?: boolean;
    } = {}
  ): Promise<RagEnhancedResponse> {
    const {
      useRag = true,
      ragThreshold = 0.6,
      maxRagResults = 3,
      fallbackToLlm = true,
    } = options;

    try {
      // Get the latest user message for RAG search
      const userMessage = this.getLatestUserMessage(messages);
      
      if (!userMessage || !useRag) {
        return await this.generateDirectLlmResponse(messages, 'RAG disabled or no user message');
      }

      // Determine if we should use RAG for this query
      const shouldUseRag = await this.shouldUseRag(userMessage);
      
      if (!shouldUseRag) {
        return await this.generateDirectLlmResponse(messages, 'Query not suitable for RAG');
      }

      // Search for relevant FAQ entries
      const ragResults = await this.ragService.searchRelevantFaqs(userMessage, maxRagResults);
      
      // Check if we have good enough results
      const goodResults = ragResults.filter(result => result.similarityScore >= ragThreshold);
      
      if (goodResults.length === 0) {
        if (fallbackToLlm) {
          return await this.generateDirectLlmResponse(messages, 'No relevant FAQ entries found');
        } else {
          return {
            response: 'I couldn\'t find relevant information in our FAQ database. Please try rephrasing your question or contact support.',
            usedRag: false,
            fallbackReason: 'No relevant FAQ entries found',
          };
        }
      }

      // Generate RAG-enhanced response
      return await this.generateRagEnhancedResponse(messages, goodResults);

    } catch (error) {
      this.logger.error('Error in RAG-enhanced generation:', error);
      
      if (fallbackToLlm) {
        return await this.generateDirectLlmResponse(messages, `RAG error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } else {
        throw error;
      }
    }
  }

  /**
   * Generate direct LLM response without RAG
   */
  private async generateDirectLlmResponse(
    messages: ChatMessage[], 
    fallbackReason?: string
  ): Promise<RagEnhancedResponse> {
    try {
      const response = await this.llmService.generateResponse(messages);
      
      return {
        response: response.content,
        usedRag: false,
        fallbackReason,
      };

    } catch (error) {
      this.logger.error('Error in direct LLM response:', error);
      throw error;
    }
  }

  /**
   * Generate RAG-enhanced response
   */
  private async generateRagEnhancedResponse(
    messages: ChatMessage[],
    ragResults: RelevantFaqResult[]
  ): Promise<RagEnhancedResponse> {
    try {
      // Create RAG context from FAQ results
      const ragContext = this.createRagContext(ragResults);
      
      // Create enhanced system message with RAG context
      const enhancedMessages = this.createRagEnhancedMessages(messages, ragContext);
      
      // Generate response with enhanced context
      const response = await this.llmService.generateResponse(enhancedMessages);
      
      return {
        response: response.content,
        usedRag: true,
        ragResults,
        ragContext,
      };

    } catch (error) {
      this.logger.error('Error generating RAG-enhanced response:', error);
      throw error;
    }
  }

  /**
   * Create RAG context from FAQ results
   */
  private createRagContext(ragResults: RelevantFaqResult[]): string {
    const contextParts = ragResults.map((result, index) => {
      return `FAQ ${index + 1} (Relevance: ${(result.similarityScore * 100).toFixed(1)}%):
Q: ${result.faqEntry.question}
A: ${result.faqEntry.answer}
Category: ${result.faqEntry.category || 'General'}`;
    });

    return `RELEVANT FAQ INFORMATION:
${contextParts.join('\n\n')}

Please use this information to provide accurate and helpful responses. If the user's question is directly answered by the FAQ information above, prioritize that information in your response. If the FAQ information is related but doesn't fully answer the question, use it as context to provide a more informed response.`;
  }

  /**
   * Create enhanced messages with RAG context
   */
  private createRagEnhancedMessages(messages: ChatMessage[], ragContext: string): ChatMessage[] {
    const enhancedMessages: ChatMessage[] = [...messages];
    
    // Find existing system message or create one
    const systemMessageIndex = enhancedMessages.findIndex(msg => msg.role === 'system');
    
    if (systemMessageIndex >= 0) {
      // Enhance existing system message
      const existingSystemMessage = enhancedMessages[systemMessageIndex];
      enhancedMessages[systemMessageIndex] = {
        ...existingSystemMessage,
        content: `${existingSystemMessage.content}\n\n${ragContext}`,
      };
    } else {
      // Add new system message with RAG context
      enhancedMessages.unshift({
        id: 'rag-system-' + Date.now(),
        role: 'system',
        content: `You are a helpful OTRS technical support assistant. Use the following FAQ information to provide accurate and helpful responses.

${ragContext}`,
        timestamp: new Date().toISOString(),
      });
    }

    return enhancedMessages;
  }

  /**
   * Get the latest user message from conversation
   */
  private getLatestUserMessage(messages: ChatMessage[]): string | null {
    for (let i = messages.length - 1; i >= 0; i--) {
      if (messages[i].role === 'user') {
        return messages[i].content;
      }
    }
    return null;
  }

  /**
   * Determine if a query should use RAG
   */
  private async shouldUseRag(query: string): Promise<boolean> {
    try {
      // Simple heuristics to determine if query is FAQ-suitable
      const lowerQuery = query.toLowerCase();
      
      // FAQ-related keywords
      const faqKeywords = [
        'how', 'what', 'why', 'when', 'where', 'can', 'does', 'is',
        'configure', 'setup', 'install', 'troubleshoot', 'error', 'problem',
        'email', 'password', 'login', 'access', 'permission', 'security',
        'otrs', 'ticket', 'sharepoint', 'teams', 'outlook', 'mfa'
      ];

      // Check if query contains FAQ-related keywords
      const hasKeywords = faqKeywords.some(keyword => lowerQuery.includes(keyword));
      
      // Check query length (too short or too long might not be suitable)
      const isGoodLength = query.length >= 10 && query.length <= 200;
      
      // Check if it's a question (contains question words or ends with ?)
      const isQuestion = lowerQuery.includes('?') || 
                        lowerQuery.startsWith('how ') ||
                        lowerQuery.startsWith('what ') ||
                        lowerQuery.startsWith('why ') ||
                        lowerQuery.startsWith('can ') ||
                        lowerQuery.startsWith('does ');

      return hasKeywords && isGoodLength && isQuestion;

    } catch (error) {
      this.logger.warn('Error determining RAG suitability:', error);
      return true; // Default to using RAG
    }
  }

  /**
   * Get RAG service statistics
   */
  async getRagStatistics() {
    try {
      return await this.ragService.getStatistics();
    } catch (error) {
      this.logger.error('Error getting RAG statistics:', error);
      return null;
    }
  }

  /**
   * Check if RAG service is available
   */
  async isRagAvailable(): Promise<boolean> {
    try {
      return await this.ragService.isAvailable();
    } catch (error) {
      this.logger.warn('RAG availability check failed:', error);
      return false;
    }
  }

  /**
   * Search FAQs directly (for testing/debugging)
   */
  async searchFaqs(query: string, topK = 5): Promise<RelevantFaqResult[]> {
    try {
      return await this.ragService.searchRelevantFaqs(query, topK);
    } catch (error) {
      this.logger.error('Error searching FAQs:', error);
      return [];
    }
  }
}
