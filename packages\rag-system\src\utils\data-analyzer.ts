import * as XLSX from 'xlsx';
import * as path from 'path';
import { logger } from './logger';

export interface FaqDataStructure {
  totalRows: number;
  columns: string[];
  sampleData: any[];
  dataTypes: Record<string, string>;
  statistics: {
    averageQuestionLength: number;
    averageAnswerLength: number;
    categoriesCount: number;
    uniqueCategories: string[];
  };
}

export class DataAnalyzer {
  private readonly excelFilePath: string;

  constructor() {
    this.excelFilePath = path.join(__dirname, '..', 'OTRS_FAQ.xlsx');
  }

  /**
   * Analyze the structure and content of the OTRS FAQ Excel file
   */
  async analyzeFaqData(): Promise<FaqDataStructure> {
    try {
      logger.info('Starting analysis of OTRS FAQ data...');

      // Read the Excel file
      const workbook = XLSX.readFile(this.excelFilePath);
      const sheetName = workbook.SheetNames[0]; // Use first sheet
      const worksheet = workbook.Sheets[sheetName];

      // Convert to JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      if (jsonData.length === 0) {
        throw new Error('Excel file is empty');
      }

      // Extract headers (first row)
      const headers = jsonData[0] as string[];
      const dataRows = jsonData.slice(1);

      logger.info(`Found ${headers.length} columns and ${dataRows.length} data rows`);

      // Analyze data structure
      const structure: FaqDataStructure = {
        totalRows: dataRows.length,
        columns: headers,
        sampleData: dataRows.slice(0, 5), // First 5 rows as sample
        dataTypes: this.analyzeDataTypes(headers, dataRows),
        statistics: this.calculateStatistics(headers, dataRows),
      };

      this.logAnalysisResults(structure);
      return structure;

    } catch (error) {
      logger.error('Error analyzing FAQ data:', error);
      throw error;
    }
  }

  /**
   * Analyze data types for each column
   */
  private analyzeDataTypes(headers: string[], dataRows: any[]): Record<string, string> {
    const dataTypes: Record<string, string> = {};

    headers.forEach((header, index) => {
      const sampleValues = dataRows
        .slice(0, 10) // Sample first 10 rows
        .map(row => row[index])
        .filter(value => value !== undefined && value !== null && value !== '');

      if (sampleValues.length === 0) {
        dataTypes[header] = 'empty';
        return;
      }

      // Determine data type
      const firstValue = sampleValues[0];
      if (typeof firstValue === 'number') {
        dataTypes[header] = 'number';
      } else if (typeof firstValue === 'string') {
        // Check if it's a date
        if (this.isDateString(firstValue)) {
          dataTypes[header] = 'date';
        } else if (firstValue.length > 100) {
          dataTypes[header] = 'long_text';
        } else {
          dataTypes[header] = 'text';
        }
      } else {
        dataTypes[header] = 'unknown';
      }
    });

    return dataTypes;
  }

  /**
   * Calculate statistics about the FAQ data
   */
  private calculateStatistics(headers: string[], dataRows: any[]): FaqDataStructure['statistics'] {
    // Try to identify question and answer columns
    const questionColumnIndex = this.findColumnIndex(headers, ['question', 'q', 'frage', 'title', 'subject']);
    const answerColumnIndex = this.findColumnIndex(headers, ['answer', 'a', 'antwort', 'response', 'solution', 'description']);
    const categoryColumnIndex = this.findColumnIndex(headers, ['category', 'kategorie', 'type', 'group', 'section']);

    let averageQuestionLength = 0;
    let averageAnswerLength = 0;
    let categoriesCount = 0;
    let uniqueCategories: string[] = [];

    // Calculate question length statistics
    if (questionColumnIndex !== -1) {
      const questionLengths = dataRows
        .map(row => row[questionColumnIndex])
        .filter(q => q && typeof q === 'string')
        .map(q => q.length);
      
      averageQuestionLength = questionLengths.length > 0 
        ? questionLengths.reduce((sum, len) => sum + len, 0) / questionLengths.length 
        : 0;
    }

    // Calculate answer length statistics
    if (answerColumnIndex !== -1) {
      const answerLengths = dataRows
        .map(row => row[answerColumnIndex])
        .filter(a => a && typeof a === 'string')
        .map(a => a.length);
      
      averageAnswerLength = answerLengths.length > 0 
        ? answerLengths.reduce((sum, len) => sum + len, 0) / answerLengths.length 
        : 0;
    }

    // Calculate category statistics
    if (categoryColumnIndex !== -1) {
      const categories = dataRows
        .map(row => row[categoryColumnIndex])
        .filter(c => c && typeof c === 'string')
        .map(c => c.trim());
      
      uniqueCategories = [...new Set(categories)];
      categoriesCount = uniqueCategories.length;
    }

    return {
      averageQuestionLength: Math.round(averageQuestionLength),
      averageAnswerLength: Math.round(averageAnswerLength),
      categoriesCount,
      uniqueCategories,
    };
  }

  /**
   * Find column index by searching for common column names
   */
  private findColumnIndex(headers: string[], searchTerms: string[]): number {
    const lowerHeaders = headers.map(h => h.toLowerCase());
    
    for (const term of searchTerms) {
      const index = lowerHeaders.findIndex(h => h.includes(term.toLowerCase()));
      if (index !== -1) {
        return index;
      }
    }
    
    return -1;
  }

  /**
   * Check if a string represents a date
   */
  private isDateString(value: string): boolean {
    const date = new Date(value);
    return !isNaN(date.getTime()) && value.length > 8;
  }

  /**
   * Log analysis results
   */
  private logAnalysisResults(structure: FaqDataStructure): void {
    logger.info('=== OTRS FAQ Data Analysis Results ===');
    logger.info(`Total FAQ entries: ${structure.totalRows}`);
    logger.info(`Columns found: ${structure.columns.join(', ')}`);
    
    logger.info('\n=== Data Types ===');
    Object.entries(structure.dataTypes).forEach(([column, type]) => {
      logger.info(`${column}: ${type}`);
    });

    logger.info('\n=== Content Statistics ===');
    logger.info(`Average question length: ${structure.statistics.averageQuestionLength} characters`);
    logger.info(`Average answer length: ${structure.statistics.averageAnswerLength} characters`);
    logger.info(`Number of categories: ${structure.statistics.categoriesCount}`);
    
    if (structure.statistics.uniqueCategories.length > 0) {
      logger.info(`Categories: ${structure.statistics.uniqueCategories.slice(0, 10).join(', ')}${structure.statistics.uniqueCategories.length > 10 ? '...' : ''}`);
    }

    logger.info('\n=== Sample Data (First 3 rows) ===');
    structure.sampleData.slice(0, 3).forEach((row, index) => {
      logger.info(`Row ${index + 1}:`, JSON.stringify(row, null, 2));
    });
  }

  /**
   * Convert Excel data to structured FAQ entries
   */
  async convertToFaqEntries(): Promise<any[]> {
    try {
      const workbook = XLSX.readFile(this.excelFilePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      // Convert to JSON with headers
      const jsonData = XLSX.utils.sheet_to_json(worksheet);
      
      logger.info(`Converted ${jsonData.length} FAQ entries from Excel`);
      return jsonData;

    } catch (error) {
      logger.error('Error converting Excel to FAQ entries:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const dataAnalyzer = new DataAnalyzer();
