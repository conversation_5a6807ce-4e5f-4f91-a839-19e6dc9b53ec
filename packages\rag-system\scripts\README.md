# RAG System Scripts

This directory contains diagnostic and utility scripts for the RAG system.

## 🔧 **Diagnostic Scripts**

### **Production Diagnostics**
- `diagnose-search-quality.ts` - Comprehensive search quality analysis
- `diagnose-embedding-service.ts` - Embedding service health check and quality testing
- `diagnose-vector-database.ts` - Vector database connectivity and performance testing

### **Development Tools**
- `benchmark-performance.ts` - Performance benchmarking for search operations
- `validate-faq-data.ts` - FAQ data validation and quality checks

## 📋 **Usage Guidelines**

### **When to Use Diagnostic Scripts**

1. **Search Quality Issues**
   ```bash
   npx ts-node scripts/diagnose-search-quality.ts
   ```
   Use when users report poor search results or irrelevant FAQ recommendations.

2. **Embedding Service Problems**
   ```bash
   npx ts-node scripts/diagnose-embedding-service.ts
   ```
   Use when experiencing embedding generation failures or quality issues.

3. **Vector Database Issues**
   ```bash
   npx ts-node scripts/diagnose-vector-database.ts
   ```
   Use when experiencing connectivity issues or slow search performance.

4. **Performance Monitoring**
   ```bash
   npx ts-node scripts/benchmark-performance.ts
   ```
   Use for regular performance monitoring or after infrastructure changes.

5. **Data Quality Validation**
   ```bash
   npx ts-node scripts/validate-faq-data.ts
   ```
   Use after uploading new FAQ data or when suspecting data quality issues.

## 🚨 **Important Notes**

- **Environment Configuration**: All scripts use `.env.test` for configuration
- **API Usage**: Scripts may consume API credits - use judiciously in production
- **Test Data**: Scripts may create test data in vector databases - clean up after use
- **Logging**: Scripts produce detailed logs for troubleshooting

## 🔒 **Security Considerations**

- Never commit real API keys to version control
- Use environment variables for all sensitive configuration
- Be cautious when running scripts against production databases
- Review script output before sharing logs (may contain sensitive data)

## 📊 **Expected Outputs**

### **Search Quality Diagnostics**
- Relevance rates (target: >80%)
- Semantic discrimination scores (target: >0.3)
- Category accuracy (target: >90%)
- Response times (target: <2s)

### **Embedding Service Diagnostics**
- Embedding generation success rate (target: >95%)
- Dimension validation (384 for sentence-transformers)
- Semantic similarity validation
- Service availability status

### **Vector Database Diagnostics**
- Connection status and latency
- Index statistics and health
- Search performance metrics
- Data integrity validation

## 🛠️ **Troubleshooting**

### **Common Issues**

1. **"No results found"**
   - Check similarity threshold (should be 0.3 for sentence-transformers)
   - Verify vector database has indexed data
   - Validate embedding service is working

2. **"Embedding service unavailable"**
   - Check vLLM server status
   - Verify API key and base URL
   - Test network connectivity

3. **"Poor search quality"**
   - Run search quality diagnostics
   - Check embedding model compatibility
   - Validate FAQ data quality

4. **"Slow performance"**
   - Run performance benchmarks
   - Check vector database performance
   - Monitor API response times

### **Getting Help**

1. Run the appropriate diagnostic script
2. Review the detailed output and logs
3. Check the troubleshooting section above
4. Consult the main README.md for configuration details
5. Contact the development team with diagnostic output if issues persist
