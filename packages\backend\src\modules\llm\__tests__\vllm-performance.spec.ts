import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { LlmService } from '../llm.service';
import { LlmProviderFactory } from '../providers/llm-provider.factory';
import { ChatMessage, Tool } from '@otrs-ai-powered/shared';

describe('vLLM Performance and Benchmarking Tests', () => {
  let llmService: LlmService;
  
  const realServerConfig = {
    apiKey: 'token-abc123-must-long-enough',
    model: 'microsoft/Phi-4-mini-instruct',
    baseUrl: 'https://antelope-premium-intensely.ngrok-free.app',
    temperature: 0.7,
    maxTokens: 2000,
    timeout: 60000,
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LlmService,
        LlmProviderFactory,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: any) => {
              const config: Record<string, any> = {
                'llm.provider': 'vllm',
                'llm.apiKey': realServerConfig.apiKey,
                'llm.model': realServerConfig.model,
                'llm.temperature': realServerConfig.temperature,
                'llm.maxTokens': realServerConfig.maxTokens,
                'llm.timeout': realServerConfig.timeout,
                'llm.baseUrl': realServerConfig.baseUrl,
              };
              return config[key] ?? defaultValue;
            }),
          },
        },
      ],
    }).compile();

    llmService = module.get<LlmService>(LlmService);
    await llmService.onModuleInit();
  }, 30000);

  describe('Response Time Benchmarks', () => {
    it('should measure response times for different message lengths', async () => {
      const testCases = [
        { name: 'Short message', content: 'Hello!' },
        { name: 'Medium message', content: 'Can you explain what OTRS is and how it works?' },
        { name: 'Long message', content: 'I need detailed help with configuring OTRS email settings, including SMTP configuration, email routing rules, and troubleshooting common email delivery issues. Please provide step-by-step instructions.' },
      ];

      const results: Array<{ name: string; responseTime: number; responseLength: number }> = [];

      for (const testCase of testCases) {
        const startTime = Date.now();
        
        const messages: ChatMessage[] = [
          {
            id: '1',
            role: 'user',
            content: testCase.content,
            timestamp: new Date().toISOString(),
          },
        ];

        const response = await llmService.generateResponse(messages);
        const responseTime = Date.now() - startTime;

        results.push({
          name: testCase.name,
          responseTime,
          responseLength: response.content.length,
        });

        expect(response.content).toBeDefined();
        expect(responseTime).toBeLessThan(45000); // Should respond within 45 seconds
      }

      console.log('\n=== Response Time Benchmarks ===');
      results.forEach(result => {
        console.log(`${result.name}: ${result.responseTime}ms (${result.responseLength} chars)`);
      });

      // Calculate averages
      const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
      const avgResponseLength = results.reduce((sum, r) => sum + r.responseLength, 0) / results.length;
      
      console.log(`Average Response Time: ${avgResponseTime.toFixed(2)}ms`);
      console.log(`Average Response Length: ${avgResponseLength.toFixed(0)} characters`);
    }, 180000);

    it('should measure tool calling performance', async () => {
      const createTicketTool: Tool = {
        type: 'function',
        name: 'create_ticket',
        description: 'Create a new OTRS support ticket',
        parameters: {
          type: 'object',
          properties: {
            title: { type: 'string', description: 'The title of the ticket' },
            description: { type: 'string', description: 'Detailed description of the issue' },
            priority: { type: 'string', enum: ['low', 'medium', 'high'] },
          },
          required: ['title', 'description'],
        },
      };

      const startTime = Date.now();
      
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'system',
          content: 'You are an OTRS support assistant. When users report issues, create tickets using the create_ticket function.',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          role: 'user',
          content: 'I have a critical database connection issue that needs immediate attention.',
          timestamp: new Date().toISOString(),
        },
      ];

      const response = await llmService.generateResponse(messages, [createTicketTool]);
      const responseTime = Date.now() - startTime;

      console.log('\n=== Tool Calling Performance ===');
      console.log(`Tool calling response time: ${responseTime}ms`);
      console.log(`Response content length: ${response.content.length} characters`);
      
      if (response.toolCalls) {
        console.log(`Number of tool calls: ${response.toolCalls.length}`);
        response.toolCalls.forEach((call, index) => {
          console.log(`Tool Call ${index + 1}: ${call.toolName}`);
          console.log(`Arguments: ${JSON.stringify(call.arguments, null, 2)}`);
        });
      } else {
        console.log('No tool calls detected');
      }

      expect(responseTime).toBeLessThan(60000); // Tool calling should complete within 60 seconds
    }, 90000);
  });

  describe('Load Testing', () => {
    it('should handle multiple sequential requests', async () => {
      const requestCount = 5;
      const results: number[] = [];

      console.log('\n=== Sequential Load Test ===');
      
      for (let i = 0; i < requestCount; i++) {
        const startTime = Date.now();
        
        const messages: ChatMessage[] = [
          {
            id: '1',
            role: 'user',
            content: `This is test request number ${i + 1}. Please respond with a brief acknowledgment.`,
            timestamp: new Date().toISOString(),
          },
        ];

        const response = await llmService.generateResponse(messages);
        const responseTime = Date.now() - startTime;
        
        results.push(responseTime);
        console.log(`Request ${i + 1}: ${responseTime}ms (${response.content.length} chars)`);
        
        expect(response.content).toBeDefined();
        expect(response.content.length).toBeGreaterThan(5);
      }

      const avgTime = results.reduce((sum, time) => sum + time, 0) / results.length;
      const minTime = Math.min(...results);
      const maxTime = Math.max(...results);
      
      console.log(`Average: ${avgTime.toFixed(2)}ms, Min: ${minTime}ms, Max: ${maxTime}ms`);
      
      // All requests should complete within reasonable time
      results.forEach(time => {
        expect(time).toBeLessThan(45000);
      });
    }, 300000);

    it('should handle concurrent requests efficiently', async () => {
      const concurrentRequests = 3;
      
      console.log('\n=== Concurrent Load Test ===');
      
      const startTime = Date.now();
      
      const promises = Array(concurrentRequests).fill(null).map((_, index) => {
        const messages: ChatMessage[] = [
          {
            id: '1',
            role: 'user',
            content: `Concurrent request ${index + 1}: What is OTRS?`,
            timestamp: new Date().toISOString(),
          },
        ];
        
        return llmService.generateResponse(messages);
      });

      const responses = await Promise.all(promises);
      const totalTime = Date.now() - startTime;
      
      console.log(`${concurrentRequests} concurrent requests completed in ${totalTime}ms`);
      console.log(`Average time per request: ${(totalTime / concurrentRequests).toFixed(2)}ms`);
      
      responses.forEach((response, index) => {
        expect(response.content).toBeDefined();
        expect(response.content.length).toBeGreaterThan(10);
        console.log(`Response ${index + 1} length: ${response.content.length} characters`);
      });
      
      // Concurrent requests should complete faster than sequential
      expect(totalTime).toBeLessThan(concurrentRequests * 45000);
    }, 180000);
  });

  describe('Quality Assessment', () => {
    it('should provide contextually relevant responses', async () => {
      const testScenarios = [
        {
          name: 'OTRS Knowledge',
          messages: [
            {
              id: '1',
              role: 'user' as const,
              content: 'What are the main components of OTRS?',
              timestamp: new Date().toISOString(),
            },
          ],
          expectedKeywords: ['ticket', 'queue', 'agent', 'customer', 'system'],
        },
        {
          name: 'Technical Support',
          messages: [
            {
              id: '1',
              role: 'system' as const,
              content: 'You are a technical support specialist.',
              timestamp: new Date().toISOString(),
            },
            {
              id: '2',
              role: 'user' as const,
              content: 'My OTRS installation is not sending email notifications.',
              timestamp: new Date().toISOString(),
            },
          ],
          expectedKeywords: ['email', 'configuration', 'smtp', 'notification', 'settings'],
        },
      ];

      console.log('\n=== Quality Assessment ===');
      
      for (const scenario of testScenarios) {
        const response = await llmService.generateResponse(scenario.messages);
        
        expect(response.content).toBeDefined();
        expect(response.content.length).toBeGreaterThan(50);
        
        const responseText = response.content.toLowerCase();
        const matchedKeywords = scenario.expectedKeywords.filter(keyword => 
          responseText.includes(keyword.toLowerCase())
        );
        
        console.log(`${scenario.name}:`);
        console.log(`  Response length: ${response.content.length} characters`);
        console.log(`  Matched keywords: ${matchedKeywords.join(', ')} (${matchedKeywords.length}/${scenario.expectedKeywords.length})`);
        console.log(`  First 100 chars: ${response.content.substring(0, 100)}...`);
        
        // Should match at least 40% of expected keywords
        expect(matchedKeywords.length).toBeGreaterThanOrEqual(Math.ceil(scenario.expectedKeywords.length * 0.4));
      }
    }, 120000);

    it('should maintain conversation context', async () => {
      console.log('\n=== Context Maintenance Test ===');
      
      // First exchange
      let messages: ChatMessage[] = [
        {
          id: '1',
          role: 'system',
          content: 'You are an OTRS support specialist. Remember the context of our conversation.',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          role: 'user',
          content: 'I am setting up OTRS for the first time and need help with email configuration.',
          timestamp: new Date().toISOString(),
        },
      ];

      let response = await llmService.generateResponse(messages);
      console.log(`First response length: ${response.content.length} characters`);
      
      // Add assistant response to context
      messages.push({
        id: '3',
        role: 'assistant',
        content: response.content,
        timestamp: new Date().toISOString(),
      });

      // Follow-up question
      messages.push({
        id: '4',
        role: 'user',
        content: 'What SMTP settings should I use?',
        timestamp: new Date().toISOString(),
      });

      response = await llmService.generateResponse(messages);
      console.log(`Follow-up response length: ${response.content.length} characters`);
      
      // The follow-up response should reference email/SMTP context
      const responseText = response.content.toLowerCase();
      const contextKeywords = ['smtp', 'email', 'configuration', 'server', 'port'];
      const matchedContext = contextKeywords.filter(keyword => responseText.includes(keyword));
      
      console.log(`Context keywords found: ${matchedContext.join(', ')}`);
      
      expect(response.content).toBeDefined();
      expect(response.content.length).toBeGreaterThan(30);
      expect(matchedContext.length).toBeGreaterThanOrEqual(2); // Should reference at least 2 context keywords
    }, 120000);
  });
});
